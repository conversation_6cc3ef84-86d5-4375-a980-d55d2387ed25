const translations = {
  // Commun
  common: {
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succ<PERSON>',
    cancel: 'Annuler',
    confirm: 'Confirmer',
    save: 'Enregistrer',
    edit: 'Modifier',
    delete: 'Supprimer',
    search: 'Rechercher',
    filter: 'Filtrer',
    sort: 'Trier',
    refresh: 'Actualiser',
    back: 'Retour',
    next: 'Suivant',
    previous: 'Précédent',
    close: 'Fermer',
    open: 'Ouvrir',
    yes: 'Oui',
    no: 'Non',
    ok: 'OK',
    apply: 'Appliquer',
    reset: 'Réinitialiser',
    clear: 'Effacer',
    generate: 'Générer',
    generating: 'Génération...',
    saving: 'Enregistrement...',
    tryAgain: 'Réessayer',
    go: 'Aller',
    view: 'Voir'
  },

  // Navigation
  nav: {
    home: 'Accueil',
    dashboard: 'Tableau de bord',
    books: 'Livres',
    members: 'Membre<PERSON>',
    reports: 'Rapports',
    settings: 'Paramètres',
    profile: 'Profil',
    logout: 'Déconnexion',
    login: 'Connexion',
    register: "S'inscrire",
    features: 'Fonctionnalités',
    services: 'Services',
    reviews: 'Avis',
    location: 'Localisation',
    analytics: 'Analytiques'
  },

  // Authentification
  auth: {
    login: 'Connexion',
    register: 'Inscription',
    logout: 'Déconnexion',
    username: "Nom d'utilisateur",
    password: 'Mot de passe',
    email: 'Email',
    confirmPassword: 'Confirmer le mot de passe',
    forgotPassword: 'Mot de passe oublié ?',
    rememberMe: 'Se souvenir de moi',
    loginSuccess: 'Connexion réussie',
    loginError: 'Identifiants invalides',
    registerSuccess: 'Inscription réussie',
    registerError: "Échec de l'inscription",
    logoutSuccess: 'Déconnexion réussie',
    invalidCredentials: "Nom d'utilisateur ou mot de passe invalide",
    accountCreated: 'Compte créé avec succès',
    passwordMismatch: 'Les mots de passe ne correspondent pas',
    emailRequired: 'Email requis',
    usernameRequired: "Nom d'utilisateur requis",
    passwordRequired: 'Mot de passe requis',
    noAccount: "Vous n'avez pas de compte ?"
  },

  // Section Hero
  hero: {
    title1: 'Recherchez et évaluez',
    title2: 'votre',
    title3: 'livre préféré',
    title4: 'sans effort',
    description: 'Embarquez pour un voyage littéraire comme jamais auparavant avec notre application de bibliothèque révolutionnaire ! Découvrez une expérience fluide qui transcende les frontières traditionnelles, où vous pouvez rechercher sans effort vos livres préférés.✨',
    startButton: 'Commencer maintenant'
  },

  // Section Fonctionnalités
  features: {
    title: 'FONCTIONNALITÉS',
    subtitle: 'Que Pouvez-Vous Faire ?',
    searchBook: 'Rechercher un livre',
    searchBookDesc: 'Trouvez sans effort votre prochaine lecture avec notre recherche de livres puissante et intuitive.',
    reviewBook: 'Critiquer un livre',
    reviewBookDesc: 'Découvrez des critiques perspicaces et partagez vos réflexions sur divers chefs-d\'œuvre littéraires sans effort.',
    wishlistBook: 'Liste de souhaits',
    wishlistBookDesc: 'Organisez vos rêves littéraires – ajoutez des livres à votre liste de souhaits pour de futures aventures et découvertes.'
  },

  // Section Services
  services: {
    title: 'SERVICES',
    subtitle: 'Les Services Pour Vous',
    rent: 'Louer',
    favoriteBook: 'votre livre préféré',
    fairlyEasy: 'assez facilement sur',
    description1: 'Visualiser, louer et organiser vos livres préférés n\'a jamais été aussi facile. Une location de bibliothèque numérique intégrée qui est simple à utiliser, BiblioSmart vous permet de passer moins de temps à gérer votre travail et plus de temps à le faire réellement !',
    description2: 'Locations sans effort, étagères personnalisées—BiblioSmart transforme la gestion des livres, améliorant votre expérience de lecture~'
  },

  // Section Locations Rapides
  quickRentals: {
    title: 'Locations Rapides de Livres :',
    dive: 'Plongez',
    into: 'dans',
    readingInstantly: 'la Lecture Instantanément',
    description1: 'Découvrez le plaisir littéraire instantané. Accédez à une vaste bibliothèque, empruntez vos lectures préférées et plongez dans des histoires captivantes en quelques minutes. La lecture rendue rapide et facile, à un clic !',
    description2: 'Débloquez un monde d\'histoires sans effort. Parcourez les genres, choisissez, louez en minutes. Gérez facilement vos aventures de lecture avec notre plateforme intuitive~'
  },

  // Section Avis
  reviews: {
    title: 'AVIS',
    subtitle: 'Avis des Autres'
  },

  // Section Localisation
  location: {
    title: 'LOCALISATION',
    subtitle: 'Emplacement de Notre Bibliothèque'
  },

  // Section Pied de Page
  footer: {
    managedBy: 'Géré Par',
    socialMedia: 'Réseaux Sociaux',
    slogan: 'Slogan',
    hashtag: '#LouerLivresPréférés',
    copyright: '© 2024 BiblioSmart. Tous droits réservés.'
  },

  // Tableau de bord
  dashboard: {
    title: 'Tableau de bord',
    overview: 'Aperçu',
    statistics: 'Statistiques',
    recentActivity: 'Activité récente',
    quickActions: 'Actions rapides',
    totalBooks: 'Emprunts actifs',
    availableBooks: 'Livres disponibles',
    borrowedBooks: 'Livres empruntés',
    totalMembers: 'Total des membres',
    activeMembers: 'Membres actifs',
    overdueBooks: 'Articles en retard',
    reservations: 'Réservations actives',
    notifications: 'Notifications',
    activeBorrowings: 'Emprunts actifs',
    activeReservations: 'Réservations actives',
    overdueItems: 'Articles en retard',
    totalFines: 'Total des amendes',
    welcome: 'Bienvenue'
  },

  // Livres
  books: {
    title: 'Livres',
    addBook: 'Ajouter un livre',
    editBook: 'Modifier le livre',
    deleteBook: 'Supprimer le livre',
    bookTitle: 'Titre du livre',
    author: 'Auteur',
    isbn: 'ISBN',
    category: 'Catégorie',
    subject: 'Sujet',
    publisher: 'Éditeur',
    publishedDate: 'Date de publication',
    pages: 'Pages',
    language: 'Langue',
    description: 'Description',
    totalCopies: 'Copies totales',
    availableCopies: 'Copies disponibles',
    location: 'Emplacement',
    status: 'Statut',
    available: 'Disponible',
    borrowed: 'Emprunté',
    reserved: 'Réservé',
    maintenance: 'Maintenance',
    searchBooks: 'Rechercher des livres',
    filterByCategory: 'Filtrer par catégorie',
    sortBy: 'Trier par',
    bookAdded: 'Livre ajouté avec succès',
    bookUpdated: 'Livre mis à jour avec succès',
    bookDeleted: 'Livre supprimé avec succès',
    bookNotFound: 'Livre non trouvé',
    noBooks: 'Aucun livre trouvé'
  },

  // Documents
  documents: {
    title: 'Documents',
    addDocument: 'Ajouter un document',
    editDocument: 'Modifier le document',
    updateDocument: 'Mettre à jour le document',
    deleteDocument: 'Supprimer le document',
    library: 'Bibliothèque de documents',
    management: 'Gestion des documents',
    searchPlaceholder: 'Rechercher des documents...',
    allTypes: 'Tous les types',
    books: 'Livres',
    periodicals: 'Périodiques',
    articles: 'Articles',
    videos: 'Vidéos',
    by: 'par',
    noDescription: 'Aucune description disponible',
    copies: 'Copies',
    available: 'Disponible',
    unavailable: 'Indisponible',
    category: 'Catégorie',
    publisher: 'Éditeur',
    noDocuments: 'Aucun document trouvé',
    // Form fields
    title: 'Titre',
    description: 'Description',
    documentType: 'Type de document',
    barcode: 'Code-barres',
    basicInformation: 'Informations de base',
    // Detail view
    quickInformation: 'Informations rapides',
    type: 'Type',
    copies: 'Exemplaires',
    noImageAvailable: 'Aucune image disponible',
    reserveDocument: 'Réserver le document',
    // Form sections
    classification: 'Classification',
    keywords: 'Mots-clés',
    subject: 'Sujet',
    bookInformation: 'Informations du livre',
    periodicalInformation: 'Informations du périodique',
    articleInformation: 'Informations de l\'article',
    videoInformation: 'Informations de la vidéo',
    author: 'Auteur',
    edition: 'Édition',
    series: 'Série',
    frequency: 'Fréquence',
    volume: 'Volume',
    issue: 'Numéro',
    journal: 'Journal',
    director: 'Réalisateur',
    duration: 'Durée',
    resolution: 'Résolution',
    publicationDetails: 'Détails de publication',
    publicationDate: 'Date de publication',
    language: 'Langue',
    physicalDetails: 'Détails physiques',
    pages: 'Pages',
    format: 'Format',
    shelfLocation: 'Emplacement sur l\'étagère',
    availability: 'Disponibilité',
    totalCopies: 'Exemplaires totaux',
    availableCopies: 'Exemplaires disponibles',
    image: 'Image',
    documentImage: 'Image du document',
    // Detail view additional
    location: 'Emplacement',
    metadata: 'Métadonnées',
    added: 'Ajouté',
    lastUpdated: 'Dernière mise à jour',
    addedBy: 'Ajouté par'
  },

  // Pagination
  pagination: {
    first: 'Premier',
    previous: 'Précédent',
    next: 'Suivant',
    last: 'Dernier',
    page: 'Page',
    of: 'de',
    showing: 'Affichage',
    goToPage: 'Aller à la page'
  },

  // Statistiques
  statistics: {
    title: 'Statistiques de la bibliothèque',
    overview: 'Aperçu',
    keyMetrics: 'Métriques clés',
    lastUpdated: 'Dernière mise à jour',
    loadError: 'Échec du chargement des statistiques',
    noData: 'Aucune statistique disponible',
    noDataMessage: 'Impossible de générer des statistiques pour le moment.',
    documentTypes: 'Types de documents',
    noDocumentTypeData: 'Aucune donnée de type de document disponible'
  },

  // Membres
  members: {
    title: 'Membres',
    addMember: 'Ajouter un membre',
    editMember: 'Modifier le membre',
    deleteMember: 'Supprimer le membre',
    memberName: 'Nom du membre',
    memberType: 'Type de membre',
    studentId: 'ID étudiant',
    phone: 'Téléphone',
    address: 'Adresse',
    joinDate: "Date d'adhésion",
    status: 'Statut',
    active: 'Actif',
    inactive: 'Inactif',
    suspended: 'Suspendu',
    borrowingHistory: "Historique d'emprunt",
    currentBorrowings: 'Emprunts actuels',
    reservations: 'Réservations',
    fines: 'Amendes',
    memberAdded: 'Membre ajouté avec succès',
    memberUpdated: 'Membre mis à jour avec succès',
    memberDeleted: 'Membre supprimé avec succès',
    memberNotFound: 'Membre non trouvé',
    noMembers: 'Aucun membre trouvé'
  },

  // Emprunt
  borrowing: {
    title: 'Emprunt',
    borrowBook: 'Emprunter un livre',
    returnBook: 'Retourner le livre',
    renewBook: 'Renouveler le livre',
    borrowDate: "Date d'emprunt",
    dueDate: 'Date d\'échéance',
    returnDate: 'Date de retour',
    renewalCount: 'Nombre de renouvellements',
    fine: 'Amende',
    overdue: 'En retard',
    returned: 'Retourné',
    borrowed: 'Emprunté',
    myBorrowings: 'Mes emprunts',
    borrowingHistory: "Historique d'emprunt",
    bookBorrowed: 'Livre emprunté avec succès',
    bookReturned: 'Livre retourné avec succès',
    bookRenewed: 'Livre renouvelé avec succès',
    borrowingFailed: "Échec de l'emprunt du livre",
    returnFailed: 'Échec du retour du livre',
    renewalFailed: 'Échec du renouvellement du livre',
    maxRenewalsReached: 'Nombre maximum de renouvellements atteint',
    bookOverdue: 'Livre en retard',
    noBorrowings: 'Aucun emprunt trouvé'
  },

  // Réservations
  reservations: {
    title: 'Réservations',
    reserveBook: 'Réserver un livre',
    cancelReservation: 'Annuler la réservation',
    reservationDate: 'Date de réservation',
    expiryDate: "Date d'expiration",
    status: 'Statut',
    pending: 'En attente',
    ready: 'Prêt',
    expired: 'Expiré',
    cancelled: 'Annulé',
    myReservations: 'Mes réservations',
    bookReserved: 'Livre réservé avec succès',
    reservationCancelled: 'Réservation annulée avec succès',
    reservationFailed: 'Échec de la réservation du livre',
    reservationExpired: 'La réservation a expiré',
    noReservations: 'Aucune réservation trouvée'
  },

  // Fonctionnalités IA
  ai: {
    title: 'Fonctionnalités IA',
    recommendations: 'Recommandations IA',
    chatAssistant: 'Assistant de chat IA',
    smartSearch: 'Recherche intelligente',
    trending: 'Livres tendance',
    forYou: 'Pour vous',
    basedOnHistory: 'Basé sur votre historique de lecture et vos préférences',
    popularBooks: 'Livres les plus populaires des 30 derniers jours',
    whyRecommended: 'Pourquoi recommandé :',
    matchesCategory: 'Correspond à votre catégorie favorite : {{category}}',
    matchesSubject: 'Correspond à votre intérêt pour : {{subject}}',
    favoriteAuthor: 'Par votre auteur favori : {{author}}',
    aiAssistantWelcome: "Bonjour ! Je suis votre assistant de bibliothèque IA. Je peux vous aider à trouver des livres, répondre aux questions sur les politiques de la bibliothèque et vous assister avec vos emprunts et réservations. Comment puis-je vous aider aujourd'hui ?",
    quickActions: 'Actions rapides :',
    findProgrammingBooks: 'Trouver des livres sur la programmation',
    checkBorrowings: 'Vérifier mes emprunts actuels',
    renewBook: 'Comment renouveler un livre ?',
    libraryPolicies: 'Quelles sont les politiques de la bibliothèque ?',
    recommendBooks: 'Recommandez-moi des livres',
    thinking: 'Réflexion...',
    typeMessage: 'Demandez-moi tout sur la bibliothèque...',
    noRecommendations: 'Commencez à emprunter des livres pour obtenir des recommandations personnalisées !',
    noTrendingData: 'Aucune donnée de tendance disponible pour le moment.',
    aiError: "Désolé, j'ai rencontré une erreur. Veuillez réessayer.",
    connectionError: "Désolé, j'ai des problèmes de connexion en ce moment. Veuillez réessayer plus tard."
  },

  // Paramètres
  settings: {
    title: 'Paramètres',
    general: 'Général',
    appearance: 'Apparence',
    language: 'Langue',
    notifications: 'Notifications',
    privacy: 'Confidentialité',
    security: 'Sécurité',
    account: 'Compte',
    darkMode: 'Mode sombre',
    lightMode: 'Mode clair',
    theme: 'Thème',
    changeLanguage: 'Changer de langue',
    selectLanguage: 'Sélectionner la langue',
    emailNotifications: 'Notifications par email',
    pushNotifications: 'Notifications push',
    smsNotifications: 'Notifications SMS',
    changePassword: 'Changer le mot de passe',
    currentPassword: 'Mot de passe actuel',
    newPassword: 'Nouveau mot de passe',
    confirmNewPassword: 'Confirmer le nouveau mot de passe',
    passwordChanged: 'Mot de passe changé avec succès',
    settingsSaved: 'Paramètres enregistrés avec succès'
  },

  // Notifications
  notifications: {
    title: 'Notifications',
    markAsRead: 'Marquer comme lu',
    markAllAsRead: 'Tout marquer comme lu',
    deleteNotification: 'Supprimer la notification',
    noNotifications: 'Aucune notification',
    bookDueSoon: 'Livre bientôt dû : {{title}}',
    bookOverdue: 'Livre en retard : {{title}}',
    bookReady: 'Livre réservé prêt : {{title}}',
    reservationExpiring: 'Réservation expirant : {{title}}',
    newBookAvailable: 'Nouveau livre disponible : {{title}}',
    accountSuspended: 'Votre compte a été suspendu',
    fineAdded: 'Amende ajoutée : {{amount}}€',
    reminderDue: 'Rappel : Livre dû demain',
    notificationRead: 'Notification marquée comme lue',
    allNotificationsRead: 'Toutes les notifications marquées comme lues'
  },

  // Erreurs
  errors: {
    general: 'Une erreur est survenue',
    networkError: 'Erreur réseau. Veuillez vérifier votre connexion.',
    serverError: 'Erreur serveur. Veuillez réessayer plus tard.',
    notFound: 'Ressource non trouvée',
    unauthorized: "Vous n'êtes pas autorisé à effectuer cette action",
    forbidden: 'Accès interdit',
    validationError: 'Veuillez vérifier votre saisie',
    sessionExpired: 'Votre session a expiré. Veuillez vous reconnecter.',
    fileUploadError: 'Échec du téléchargement du fichier',
    fileSizeError: 'Taille de fichier trop importante',
    fileTypeError: 'Type de fichier invalide'
  },

  // Messages de succès
  success: {
    operationCompleted: 'Opération terminée avec succès',
    dataSaved: 'Données enregistrées avec succès',
    dataUpdated: 'Données mises à jour avec succès',
    dataDeleted: 'Données supprimées avec succès',
    emailSent: 'Email envoyé avec succès',
    passwordReset: 'Mot de passe réinitialisé avec succès',
    profileUpdated: 'Profil mis à jour avec succès',
    settingsUpdated: 'Paramètres mis à jour avec succès'
  }
};

export default translations;
