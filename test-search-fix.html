<!DOCTYPE html>
<html>
<head>
    <title>🔍 Test Search Fix</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
            background: #fafafa;
        }
        .result { 
            background: #f5f5f5; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error { 
            background: #ffebee; 
            color: #c62828; 
            border-left: 4px solid #c62828;
        }
        .success { 
            background: #e8f5e8; 
            color: #2e7d32; 
            border-left: 4px solid #2e7d32;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            border-left: 4px solid #f57c00;
        }
        button { 
            padding: 10px 15px; 
            margin: 5px; 
            cursor: pointer; 
            border: none;
            border-radius: 4px;
            background: #2196F3;
            color: white;
            font-weight: bold;
        }
        button:hover {
            background: #1976D2;
        }
        input { 
            padding: 8px; 
            margin: 5px; 
            width: 200px; 
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online { background: #4CAF50; color: white; }
        .status.offline { background: #F44336; color: white; }
        .status.unknown { background: #9E9E9E; color: white; }
        h1 { color: #333; text-align: center; }
        h3 { color: #555; border-bottom: 2px solid #2196F3; padding-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Search Fix - BiblioAI</h1>
        
        <div class="test-section">
            <h3>🏥 1. Service Health Check</h3>
            <button onclick="checkAllServices()">Check All Services</button>
            <div id="health-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📚 2. Document Service Test</h3>
            <button onclick="testDocumentService()">Test Document Service</button>
            <button onclick="initDemoData()">Initialize Demo Data</button>
            <div id="document-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 3. Search Service Test</h3>
            <button onclick="testSearchService()">Test Search Service</button>
            <button onclick="reindexDocuments()">Reindex Documents</button>
            <div id="search-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 4. Search Comparison Test</h3>
            <input type="text" id="search-term" placeholder="Enter search term" value="data">
            <button onclick="compareSearchResults()">Compare Search Results</button>
            <div id="comparison-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 5. Frontend Integration Test</h3>
            <button onclick="testFrontendIntegration()">Test Frontend Integration</button>
            <div id="frontend-result" class="result"></div>
        </div>
    </div>

    <script>
        const SERVICES = {
            'API Gateway': 'http://localhost:5000',
            'Document Service': 'http://localhost:5001',
            'Auth Service': 'http://localhost:5002',
            'Classification Service': 'http://localhost:5003',
            'Search Service': 'http://localhost:5004',
            'Member Service': 'http://localhost:5006',
            'Notification Service': 'http://localhost:5007'
        };

        function logResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function checkAllServices() {
            logResult('health-result', '🔄 Checking all services...', 'warning');
            
            let results = '🏥 Service Health Check Results:\n\n';
            let allHealthy = true;

            for (const [name, url] of Object.entries(SERVICES)) {
                try {
                    const response = await fetch(`${url}/health`, { timeout: 5000 });
                    if (response.ok) {
                        results += `✅ ${name}: ONLINE\n`;
                    } else {
                        results += `❌ ${name}: ERROR (${response.status})\n`;
                        allHealthy = false;
                    }
                } catch (error) {
                    results += `❌ ${name}: OFFLINE (${error.message})\n`;
                    allHealthy = false;
                }
            }

            results += `\n📊 Overall Status: ${allHealthy ? '✅ ALL SERVICES HEALTHY' : '❌ SOME SERVICES DOWN'}`;
            logResult('health-result', results, allHealthy ? 'success' : 'error');
        }

        async function testDocumentService() {
            logResult('document-result', '🔄 Testing Document Service...', 'warning');
            
            try {
                // Test basic connection
                const healthResponse = await fetch('http://localhost:5001/health');
                if (!healthResponse.ok) {
                    throw new Error('Document service is not running');
                }

                // Test debug endpoint
                const debugResponse = await fetch('http://localhost:5001/debug/documents');
                const debugData = await debugResponse.json();

                let result = '📚 Document Service Test Results:\n\n';
                result += `✅ Service Status: ONLINE\n`;
                result += `📊 Total Documents: ${debugData.documents.length}\n\n`;

                if (debugData.documents.length === 0) {
                    result += '⚠️  No documents found! Click "Initialize Demo Data" to create test documents.\n';
                } else {
                    result += '📖 Available Documents:\n';
                    debugData.documents.forEach(doc => {
                        result += `  - "${doc.title}" by ${doc.author || 'N/A'} (${doc.type})\n`;
                    });
                }

                logResult('document-result', result, debugData.documents.length > 0 ? 'success' : 'warning');
            } catch (error) {
                logResult('document-result', `❌ Document Service Test Failed:\n${error.message}`, 'error');
            }
        }

        async function initDemoData() {
            logResult('document-result', '🔄 Initializing demo data...', 'warning');
            
            try {
                const response = await fetch('http://localhost:5001/test-db');
                const data = await response.json();

                let result = '🎯 Demo Data Initialization:\n\n';
                result += `✅ Database Connection: OK\n`;
                result += `📊 Documents Found: ${data.document_count}\n\n`;

                if (data.documents && data.documents.length > 0) {
                    result += '📖 Documents in Database:\n';
                    data.documents.forEach(doc => {
                        result += `  - "${doc.title}" (${doc.type})\n`;
                    });
                } else {
                    result += '⚠️  No documents found. Demo data should be auto-created on service start.\n';
                }

                logResult('document-result', result, 'success');
            } catch (error) {
                logResult('document-result', `❌ Demo Data Initialization Failed:\n${error.message}`, 'error');
            }
        }

        async function testSearchService() {
            logResult('search-result', '🔄 Testing Search Service...', 'warning');
            
            try {
                // Test health
                const healthResponse = await fetch('http://localhost:5004/health');
                if (!healthResponse.ok) {
                    throw new Error('Search service is not running');
                }

                // Test stats
                const statsResponse = await fetch('http://localhost:5004/stats', {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token') || 'test'}` }
                });

                let result = '🔍 Search Service Test Results:\n\n';
                result += `✅ Service Status: ONLINE\n`;

                if (statsResponse.ok) {
                    const stats = await statsResponse.json();
                    result += `📊 Indexed Documents: ${stats.total_indexed}\n`;
                    result += `📋 By Type: ${JSON.stringify(stats.by_type)}\n`;
                    result += `🏷️  By Category: ${JSON.stringify(stats.by_category)}\n`;
                } else {
                    result += `⚠️  Stats unavailable (${statsResponse.status})\n`;
                }

                logResult('search-result', result, 'success');
            } catch (error) {
                logResult('search-result', `❌ Search Service Test Failed:\n${error.message}`, 'error');
            }
        }

        async function reindexDocuments() {
            logResult('search-result', '🔄 Reindexing documents...', 'warning');
            
            try {
                const response = await fetch('http://localhost:5000/api/search/reindex', {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token') || 'test'}` }
                });

                const data = await response.json();

                let result = '🔄 Document Reindexing Results:\n\n';
                if (response.ok) {
                    result += `✅ Reindexing Status: SUCCESS\n`;
                    result += `📊 Documents Indexed: ${data.indexed_documents}\n`;
                    result += `💬 Message: ${data.message}\n`;
                } else {
                    result += `❌ Reindexing Status: FAILED\n`;
                    result += `💬 Error: ${data.message}\n`;
                }

                logResult('search-result', result, response.ok ? 'success' : 'error');
            } catch (error) {
                logResult('search-result', `❌ Reindexing Failed:\n${error.message}`, 'error');
            }
        }

        async function compareSearchResults() {
            const searchTerm = document.getElementById('search-term').value;
            if (!searchTerm) {
                logResult('comparison-result', '⚠️  Please enter a search term', 'warning');
                return;
            }

            logResult('comparison-result', `🔄 Comparing search results for "${searchTerm}"...`, 'warning');
            
            try {
                // Test Document Service search
                const docResponse = await fetch(`http://localhost:5000/api/documents?search=${encodeURIComponent(searchTerm)}`, {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token') || 'test'}` }
                });

                // Test Search Service search
                const searchResponse = await fetch(`http://localhost:5000/api/search?q=${encodeURIComponent(searchTerm)}`, {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token') || 'test'}` }
                });

                let result = `🎯 Search Comparison for "${searchTerm}":\n\n`;

                // Document Service Results
                if (docResponse.ok) {
                    const docData = await docResponse.json();
                    result += `📚 Document Service Results: ${docData.total || 0} found\n`;
                    if (docData.documents && docData.documents.length > 0) {
                        docData.documents.forEach(doc => {
                            result += `  - "${doc.title}" by ${doc.author || 'N/A'}\n`;
                        });
                    }
                } else {
                    result += `❌ Document Service: ERROR (${docResponse.status})\n`;
                }

                result += '\n';

                // Search Service Results
                if (searchResponse.ok) {
                    const searchData = await searchResponse.json();
                    result += `🔍 Search Service Results: ${searchData.total || 0} found\n`;
                    if (searchData.documents && searchData.documents.length > 0) {
                        searchData.documents.forEach(doc => {
                            result += `  - "${doc.title}" by ${doc.author || 'N/A'}\n`;
                        });
                    }
                } else {
                    result += `❌ Search Service: ERROR (${searchResponse.status})\n`;
                }

                // Analysis
                result += '\n📊 Analysis:\n';
                if (docResponse.ok && searchResponse.ok) {
                    const docData = await docResponse.json();
                    const searchData = await searchResponse.json();
                    
                    if (docData.total === searchData.total) {
                        result += `✅ Both services return same count (${docData.total})\n`;
                    } else {
                        result += `⚠️  Different counts: Document(${docData.total}) vs Search(${searchData.total})\n`;
                    }
                }

                logResult('comparison-result', result, 'success');
            } catch (error) {
                logResult('comparison-result', `❌ Search Comparison Failed:\n${error.message}`, 'error');
            }
        }

        async function testFrontendIntegration() {
            logResult('frontend-result', '🔄 Testing frontend integration...', 'warning');
            
            let result = '🌐 Frontend Integration Test:\n\n';
            
            // Check if we're running from the frontend
            if (window.location.hostname === 'localhost' && window.location.port === '3000') {
                result += '✅ Running from React frontend\n';
                result += '🔗 API calls will use relative URLs\n';
            } else {
                result += '⚠️  Running standalone (not from React frontend)\n';
                result += '🔗 API calls will use absolute URLs\n';
            }

            // Test API Gateway
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    result += '✅ API Gateway: ACCESSIBLE\n';
                } else {
                    result += '❌ API Gateway: ERROR\n';
                }
            } catch (error) {
                result += '❌ API Gateway: UNREACHABLE\n';
            }

            result += '\n🎯 Next Steps:\n';
            result += '1. Start the React frontend: npm start\n';
            result += '2. Navigate to the search page\n';
            result += '3. Try searching for "data"\n';
            result += '4. Should show only "Data Science with Python"\n';

            logResult('frontend-result', result, 'success');
        }

        // Auto-run health check on page load
        window.onload = function() {
            checkAllServices();
        };
    </script>
</body>
</html>
