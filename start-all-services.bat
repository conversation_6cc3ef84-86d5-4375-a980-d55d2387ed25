@echo off
echo 🚀 Starting BiblioAI Microservices...
echo.

REM Create log directory
if not exist "logs" mkdir logs

echo 📊 Starting Document Service (Port 5001)...
start "Document Service" cmd /k "cd microservices\document-service && python app.py > ..\..\logs\document-service.log 2>&1"
timeout /t 3 /nobreak > nul

echo 🔐 Starting Auth Service (Port 5002)...
start "Auth Service" cmd /k "cd microservices\auth-service && python app.py > ..\..\logs\auth-service.log 2>&1"
timeout /t 3 /nobreak > nul

echo 🏷️ Starting Classification Service (Port 5003)...
start "Classification Service" cmd /k "cd microservices\classification-service && python app.py > ..\..\logs\classification-service.log 2>&1"
timeout /t 3 /nobreak > nul

echo 🔍 Starting Search Service (Port 5004)...
start "Search Service" cmd /k "cd microservices\search-service && python app.py > ..\..\logs\search-service.log 2>&1"
timeout /t 3 /nobreak > nul

echo 🌐 Starting API Gateway (Port 5000)...
start "API Gateway" cmd /k "cd microservices\api-gateway && python app.py > ..\..\logs\api-gateway.log 2>&1"
timeout /t 3 /nobreak > nul

echo 👥 Starting Member Service (Port 5006)...
start "Member Service" cmd /k "cd microservices\member-service && python app.py > ..\..\logs\member-service.log 2>&1"
timeout /t 3 /nobreak > nul

echo 🔔 Starting Notification Service (Port 5007)...
start "Notification Service" cmd /k "cd microservices\notification-service && python app.py > ..\..\logs\notification-service.log 2>&1"
timeout /t 5 /nobreak > nul

echo.
echo ✅ All services started!
echo.
echo 📋 Service URLs:
echo   - API Gateway:        http://localhost:5000
echo   - Document Service:   http://localhost:5001
echo   - Auth Service:       http://localhost:5002
echo   - Classification:     http://localhost:5003
echo   - Search Service:     http://localhost:5004
echo   - Member Service:     http://localhost:5006
echo   - Notification:       http://localhost:5007
echo.
echo 🌐 Frontend: http://localhost:3000
echo.
echo 📝 Logs are saved in the 'logs' directory
echo.
echo ⚠️  Wait 10-15 seconds for all services to fully start before testing
echo.
pause
