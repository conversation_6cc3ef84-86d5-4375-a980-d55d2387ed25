import axios from 'axios';

const API_URL = 'http://localhost:5000/api';

// Create axios instance with auth header
const createAuthHeader = () => {
  const token = localStorage.getItem('token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Search documents using the dedicated search service
export const searchDocuments = async (params = {}) => {
  try {
    // Map frontend parameters to search service parameters
    const searchParams = {};
    
    if (params.search) {
      searchParams.q = params.search;
    }
    
    if (params.type) {
      searchParams.type = params.type;
    }
    
    if (params.category) {
      searchParams.category = params.category;
    }
    
    if (params.available_only !== undefined) {
      searchParams.available_only = params.available_only;
    }
    
    if (params.page) {
      searchParams.page = params.page;
    }
    
    if (params.per_page) {
      searchParams.per_page = params.per_page;
    }

    console.log('[SEARCH SERVICE] Sending search request with params:', searchParams);
    
    const response = await axios.get(`${API_URL}/search`, {
      headers: createAuthHeader(),
      params: searchParams
    });
    
    console.log('[SEARCH SERVICE] Search response:', response.data);
    return response.data;
  } catch (error) {
    console.error('[SEARCH SERVICE] Search error:', error);
    throw error.response?.data || { message: 'Search service error' };
  }
};

// Get search suggestions
export const getSearchSuggestions = async (query) => {
  try {
    const response = await axios.get(`${API_URL}/search/suggestions`, {
      headers: createAuthHeader(),
      params: { q: query }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Reindex all documents for search
export const reindexDocuments = async () => {
  try {
    const response = await axios.post(`${API_URL}/search/reindex`, {}, {
      headers: createAuthHeader()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Fallback to document service if search service fails
export const fallbackGetDocuments = async (params = {}) => {
  try {
    console.log('[SEARCH SERVICE] Using fallback document service');
    const response = await axios.get(`${API_URL}/documents`, {
      headers: createAuthHeader(),
      params
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Smart search function that tries search service first, then falls back to document service
export const smartSearch = async (params = {}) => {
  try {
    // If there's a search term, use the search service
    if (params.search && params.search.trim()) {
      console.log('[SEARCH SERVICE] Using search service for query:', params.search);
      return await searchDocuments(params);
    } else {
      // For browsing without search, use document service
      console.log('[SEARCH SERVICE] Using document service for browsing');
      return await fallbackGetDocuments(params);
    }
  } catch (error) {
    console.warn('[SEARCH SERVICE] Search service failed, falling back to document service:', error.message);
    // If search service fails, fall back to document service
    return await fallbackGetDocuments(params);
  }
};
