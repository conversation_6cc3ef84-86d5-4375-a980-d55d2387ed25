import React, { useState, useEffect } from 'react';
import { getDocuments } from '../../services/documentService';
import { createReservation } from '../../services/memberService';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const BookSearch = () => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [reservingId, setReservingId] = useState(null);

  // Theme and Language hooks
  const { getCurrentColors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    category: '',
    available_only: true
  });

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchDocuments();
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [filters]);

  // Fetch documents when page changes
  useEffect(() => {
    fetchDocuments();
  }, [currentPage]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError('');

      // Clean up filters - remove empty strings
      const cleanFilters = Object.fromEntries(
        Object.entries(filters).filter(([key, value]) => {
          if (typeof value === 'string') {
            return value.trim() !== '';
          }
          return value !== null && value !== undefined;
        })
      );

      const params = {
        page: currentPage,
        per_page: 6,
        ...cleanFilters
      };

      console.log('Fetching documents with params:', params);
      const response = await getDocuments(params);

      if (response && response.documents) {
        setDocuments(response.documents);
        setTotalPages(response.pages || 1);
      } else {
        setDocuments([]);
        setTotalPages(1);
      }
    } catch (err) {
      console.error('Error fetching documents:', err);
      setError(err.message || 'Failed to fetch documents. Please try again.');
      setDocuments([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchDocuments();
  };

  const handleFilterChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFilters(prev => ({
      ...prev,
      [name]: newValue
    }));

    // Reset to first page when filters change
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      type: '',
      category: '',
      available_only: true
    });
    setCurrentPage(1);
  };

  const handleReserveBook = async (documentId, documentTitle) => {
    if (!window.confirm(`Are you sure you want to reserve "${documentTitle}"?`)) {
      return;
    }

    try {
      setReservingId(documentId);
      await createReservation({ document_id: documentId });
      
      // Refresh the documents list to update availability
      await fetchDocuments();
      
      alert(`Successfully reserved "${documentTitle}"!`);
    } catch (err) {
      alert(err.message || 'Failed to reserve book');
    } finally {
      setReservingId(null);
    }
  };

  const getDocumentTypeIcon = (type) => {
    switch (type) {
      case 'book': return '📚';
      case 'periodical': return '📰';
      case 'article': return '📄';
      case 'video': return '🎥';
      default: return '📄';
    }
  };

  const isAvailable = (document) => {
    return document.available_copies > 0;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3
          className="text-lg font-medium transition-colors duration-200"
          style={{ color: colors.text }}
        >
          {t('books.searchAndReserve') || 'Search & Reserve Books'}
        </h3>
        <button
          onClick={fetchDocuments}
          className="inline-flex items-center px-3 py-2 border shadow-sm text-sm leading-4 font-medium rounded-md transition-colors duration-200"
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
            color: colors.text
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = isDarkMode ? '#374151' : '#f9fafb';
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = colors.surface;
          }}
        >
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {t('common.refresh') || 'Refresh'}
        </button>
      </div>

      {/* Search and Filters */}
      <div
        className="shadow rounded-lg p-6 transition-colors duration-200"
        style={{ backgroundColor: colors.surface }}
      >
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label
                htmlFor="search"
                className="block text-sm font-medium mb-1 transition-colors duration-200"
                style={{ color: colors.text }}
              >
                {t('common.search') || 'Search'}
              </label>
              <input
                type="text"
                id="search"
                name="search"
                placeholder={t('documents.searchPlaceholder') || 'Title, author, ISBN...'}
                value={filters.search}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                  focusRingColor: colors.primary
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = colors.primary;
                  e.target.style.boxShadow = `0 0 0 2px ${colors.primary}20`;
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = colors.border;
                  e.target.style.boxShadow = 'none';
                }}
              />
            </div>

            <div>
              <label
                htmlFor="type"
                className="block text-sm font-medium mb-1 transition-colors duration-200"
                style={{ color: colors.text }}
              >
                {t('documents.type') || 'Type'}
              </label>
              <select
                id="type"
                name="type"
                value={filters.type}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = colors.primary;
                  e.target.style.boxShadow = `0 0 0 2px ${colors.primary}20`;
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = colors.border;
                  e.target.style.boxShadow = 'none';
                }}
              >
                <option value="">{t('documents.allTypes') || 'All Types'}</option>
                <option value="book">{t('documents.books') || 'Books'}</option>
                <option value="periodical">{t('documents.periodicals') || 'Periodicals'}</option>
                <option value="article">{t('documents.articles') || 'Articles'}</option>
                <option value="video">{t('documents.videos') || 'Videos'}</option>
              </select>
            </div>

            <div>
              <label
                htmlFor="category"
                className="block text-sm font-medium mb-1 transition-colors duration-200"
                style={{ color: colors.text }}
              >
                {t('documents.category') || 'Category'}
              </label>
              <input
                type="text"
                id="category"
                name="category"
                placeholder={t('documents.categoryPlaceholder') || 'Category...'}
                value={filters.category}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = colors.primary;
                  e.target.style.boxShadow = `0 0 0 2px ${colors.primary}20`;
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = colors.border;
                  e.target.style.boxShadow = 'none';
                }}
              />
            </div>

            <div className="flex items-end space-x-2">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white transition-colors duration-200 disabled:opacity-50"
                style={{
                  backgroundColor: loading ? colors.textSecondary : colors.primary,
                  cursor: loading ? 'not-allowed' : 'pointer'
                }}
                onMouseEnter={(e) => {
                  if (!loading) {
                    e.target.style.backgroundColor = isDarkMode ? '#3b82f6' : '#2563eb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    e.target.style.backgroundColor = colors.primary;
                  }
                }}
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {t('common.searching') || 'Searching...'}
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    {t('common.search') || 'Search'}
                  </>
                )}
              </button>

              <button
                type="button"
                onClick={clearFilters}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md transition-colors duration-200 disabled:opacity-50"
                style={{
                  backgroundColor: colors.surface,
                  borderColor: colors.border,
                  color: colors.textSecondary
                }}
                onMouseEnter={(e) => {
                  if (!loading) {
                    e.target.style.backgroundColor = isDarkMode ? '#374151' : '#f3f4f6';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    e.target.style.backgroundColor = colors.surface;
                  }
                }}
                title={t('common.clearFilters') || 'Clear all filters'}
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="available_only"
              name="available_only"
              checked={filters.available_only}
              onChange={handleFilterChange}
              className="h-4 w-4 rounded transition-colors duration-200"
              style={{
                accentColor: colors.primary,
                borderColor: colors.border
              }}
            />
            <label
              htmlFor="available_only"
              className="ml-2 block text-sm transition-colors duration-200"
              style={{ color: colors.text }}
            >
              {t('documents.showOnlyAvailable') || 'Show only available documents'}
            </label>
          </div>
        </form>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div
          className="border rounded-md p-4 transition-colors duration-200"
          style={{
            backgroundColor: isDarkMode ? '#7f1d1d' : '#fef2f2',
            borderColor: isDarkMode ? '#991b1b' : '#fecaca'
          }}
        >
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
                style={{ color: isDarkMode ? '#fecaca' : '#dc2626' }}
              >
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3
                className="text-sm font-medium"
                style={{ color: isDarkMode ? '#fecaca' : '#dc2626' }}
              >
                {t('common.error') || 'Error'}
              </h3>
              <div className="mt-2 text-sm" style={{ color: isDarkMode ? '#fca5a5' : '#b91c1c' }}>
                <p>{error}</p>
              </div>
              <div className="mt-3">
                <button
                  onClick={() => {
                    setError('');
                    fetchDocuments();
                  }}
                  className="text-sm font-medium underline transition-colors duration-200"
                  style={{ color: isDarkMode ? '#fecaca' : '#dc2626' }}
                >
                  {t('common.tryAgain') || 'Try Again'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Documents Grid */}
      {!loading && !error && (
        <>
          {documents.length === 0 ? (
            <div className="text-center py-8">
              <div
                className="mx-auto h-12 w-12 transition-colors duration-200"
                style={{ color: colors.textSecondary }}
              >
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3
                className="mt-2 text-sm font-medium transition-colors duration-200"
                style={{ color: colors.text }}
              >
                {t('documents.noDocuments') || 'No documents found'}
              </h3>
              <p
                className="mt-1 text-sm transition-colors duration-200"
                style={{ color: colors.textSecondary }}
              >
                {t('documents.tryAdjustingCriteria') || 'Try adjusting your search criteria.'}
              </p>
              {(filters.search || filters.type || filters.category) && (
                <button
                  onClick={clearFilters}
                  className="mt-3 text-sm font-medium underline transition-colors duration-200"
                  style={{ color: colors.primary }}
                >
                  {t('common.clearFilters') || 'Clear all filters'}
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {documents.map((document) => (
                <div
                  key={document.id}
                  className="shadow rounded-lg overflow-hidden transition-colors duration-200"
                  style={{ backgroundColor: colors.surface }}
                >
                  {/* Document Image */}
                  <div className="h-48 bg-gray-200 flex items-center justify-center">
                    {document.image_path ? (
                      <img
                        src={`http://localhost:5005/uploads/${document.image_path.replace(/^uploads[\\/]/, '').replace(/\\/g, '/')}`}
                        alt={document.title}
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          e.target.onError = null;
                          e.target.src = `${process.env.PUBLIC_URL}/placeholder-document.png`;
                        }}
                      />
                    ) : (
                      <div className="text-4xl text-gray-400">
                        {getDocumentTypeIcon(document.document_type)}
                      </div>
                    )}
                  </div>

                  {/* Document Info */}
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4
                          className="text-lg font-medium truncate transition-colors duration-200"
                          title={document.title}
                          style={{ color: colors.text }}
                        >
                          {document.title}
                        </h4>
                        {document.author && (
                          <p
                            className="text-sm mt-1 transition-colors duration-200"
                            style={{ color: colors.textSecondary }}
                          >
                            by {document.author}
                          </p>
                        )}
                        <div
                          className="mt-2 flex items-center space-x-4 text-xs transition-colors duration-200"
                          style={{ color: colors.textSecondary }}
                        >
                          <span className="inline-flex items-center px-2 py-1 rounded-full bg-gray-100">
                            {document.document_type}
                          </span>
                          {document.category && (
                            <span>{document.category}</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Availability and Actions */}
                    <div className="mt-4 flex items-center justify-between">
                      <div className="flex items-center">
                        {isAvailable(document) ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            ✓ Available ({document.available_copies}/{document.total_copies})
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            ✗ Unavailable
                          </span>
                        )}
                      </div>

                      {isAvailable(document) && (
                        <button
                          onClick={() => handleReserveBook(document.id, document.title)}
                          disabled={reservingId === document.id}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                        >
                          {reservingId === document.id ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Reserving...
                            </>
                          ) : (
                            <>
                              <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                              Reserve
                            </>
                          )}
                        </button>
                      )}
                    </div>

                    {/* Additional Info */}
                    {document.description && (
                      <div className="mt-3">
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {document.description}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div
              className="flex items-center justify-between border-t px-4 py-3 sm:px-6 rounded-lg transition-colors duration-200"
              style={{
                borderTopColor: colors.border,
                backgroundColor: colors.surface
              }}
            >
              <div className="flex flex-1 justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Page <span className="font-medium">{currentPage}</span> of{' '}
                    <span className="font-medium">{totalPages}</span>
                  </p>
                </div>
                <div>
                  <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50"
                    >
                      <span className="sr-only">Previous</span>
                      <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                      </svg>
                    </button>
                    <span className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300">
                      {currentPage}
                    </span>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50"
                    >
                      <span className="sr-only">Next</span>
                      <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default BookSearch;
