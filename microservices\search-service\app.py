from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import os
import requests
import re

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'search-service-secret')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
    'DATABASE_URI',
    'mysql+pymysql://root:@localhost:3306/biblioai_search'  # XAMPP MySQL configuration
)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['AUTH_SERVICE_URL'] = os.environ.get('AUTH_SERVICE_URL', 'http://localhost:5001')
app.config['DOCUMENT_SERVICE_URL'] = os.environ.get('DOCUMENT_SERVICE_URL', 'http://localhost:5005')

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)

# Search Index Model
class SearchIndex(db.Model):
    __tablename__ = 'search_index'

    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, nullable=False, unique=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text)  # Concatenated searchable content
    document_type = db.Column(db.String(50))
    category = db.Column(db.String(100))
    subject = db.Column(db.String(100))
    author = db.Column(db.String(200))
    keywords = db.Column(db.Text)
    language = db.Column(db.String(50))
    created_at = db.Column(db.DateTime)

    def to_dict(self):
        return {
            'document_id': self.document_id,
            'title': self.title,
            'document_type': self.document_type,
            'category': self.category,
            'subject': self.subject,
            'author': self.author,
            'keywords': self.keywords,
            'language': self.language
        }

# Authentication middleware
def verify_token():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None

    token = auth_header.split(' ')[1]
    try:
        response = requests.get(
            f"{app.config['AUTH_SERVICE_URL']}/verify",
            headers={'Authorization': f'Bearer {token}'},
            timeout=5
        )
        if response.status_code == 200:
            return response.json()['user']
    except:
        pass
    return None

def require_auth(f):
    def wrapper(*args, **kwargs):
        user = verify_token()
        if not user:
            return jsonify({'message': 'Authentication required'}), 401
        request.current_user = user
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

# Routes
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'service': 'search-service'}), 200

@app.route('/search', methods=['GET'])
@require_auth
def search_documents():
    query_text = request.args.get('q', '')
    document_type = request.args.get('type', '')
    category = request.args.get('category', '')
    subject = request.args.get('subject', '')
    author = request.args.get('author', '')
    language = request.args.get('language', '')
    available_only = request.args.get('available_only', 'false').lower() == 'true'
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    # Build search query
    query = SearchIndex.query

    if query_text:
        search_term = f"%{query_text}%"
        query = query.filter(
            db.or_(
                SearchIndex.title.ilike(search_term),
                SearchIndex.content.ilike(search_term),
                SearchIndex.keywords.ilike(search_term),
                SearchIndex.author.ilike(search_term)
            )
        )

    if document_type:
        query = query.filter(SearchIndex.document_type == document_type)

    if category:
        query = query.filter(SearchIndex.category.ilike(f"%{category}%"))

    if subject:
        query = query.filter(SearchIndex.subject.ilike(f"%{subject}%"))

    if author:
        query = query.filter(SearchIndex.author.ilike(f"%{author}%"))

    if language:
        query = query.filter(SearchIndex.language == language)

    # Execute search
    results = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    # Get full document details from document service
    document_ids = [result.document_id for result in results.items]
    documents = []

    if document_ids:
        try:
            auth_header = request.headers.get('Authorization')
            for doc_id in document_ids:
                response = requests.get(
                    f"{app.config['DOCUMENT_SERVICE_URL']}/documents/{doc_id}",
                    headers={'Authorization': auth_header},
                    timeout=5
                )
                if response.status_code == 200:
                    doc_data = response.json()
                    # Filter by availability if requested
                    if not available_only or doc_data.get('available_copies', 0) > 0:
                        documents.append(doc_data)
        except Exception as e:
            print(f"Error fetching document details: {e}")

    return jsonify({
        'documents': documents,
        'total': results.total,
        'pages': results.pages,
        'current_page': page,
        'per_page': per_page,
        'query': query_text
    }), 200

@app.route('/suggestions', methods=['GET'])
@require_auth
def get_suggestions():
    query_text = request.args.get('q', '')
    limit = request.args.get('limit', 5, type=int)

    if not query_text or len(query_text) < 2:
        return jsonify({'suggestions': []}), 200

    search_term = f"%{query_text}%"

    # Get title suggestions
    title_suggestions = db.session.query(SearchIndex.title).filter(
        SearchIndex.title.ilike(search_term)
    ).distinct().limit(limit).all()

    # Get author suggestions
    author_suggestions = db.session.query(SearchIndex.author).filter(
        SearchIndex.author.ilike(search_term),
        SearchIndex.author.isnot(None)
    ).distinct().limit(limit).all()

    # Get category suggestions
    category_suggestions = db.session.query(SearchIndex.category).filter(
        SearchIndex.category.ilike(search_term),
        SearchIndex.category.isnot(None)
    ).distinct().limit(limit).all()

    suggestions = []
    suggestions.extend([{'type': 'title', 'value': title[0]} for title in title_suggestions])
    suggestions.extend([{'type': 'author', 'value': author[0]} for author in author_suggestions])
    suggestions.extend([{'type': 'category', 'value': category[0]} for category in category_suggestions])

    return jsonify({'suggestions': suggestions[:limit]}), 200

@app.route('/index', methods=['POST'])
@require_auth
def index_document():
    """Index a document for search"""
    data = request.get_json()

    required_fields = ['document_id', 'title']
    if not all(field in data for field in required_fields):
        return jsonify({'message': 'Missing required fields'}), 400

    try:
        # Check if document already indexed
        existing = SearchIndex.query.filter_by(document_id=data['document_id']).first()

        # Create search content
        content_parts = [
            data.get('title', ''),
            data.get('description', ''),
            data.get('publisher', ''),
            data.get('keywords', '')
        ]
        content = ' '.join(filter(None, content_parts))

        if existing:
            # Update existing index
            existing.title = data['title']
            existing.content = content
            existing.document_type = data.get('document_type')
            existing.category = data.get('category')
            existing.subject = data.get('subject')
            existing.author = data.get('author')
            existing.keywords = data.get('keywords')
            existing.language = data.get('language')
        else:
            # Create new index entry
            index_entry = SearchIndex(
                document_id=data['document_id'],
                title=data['title'],
                content=content,
                document_type=data.get('document_type'),
                category=data.get('category'),
                subject=data.get('subject'),
                author=data.get('author'),
                keywords=data.get('keywords'),
                language=data.get('language'),
                created_at=data.get('created_at')
            )
            db.session.add(index_entry)

        db.session.commit()
        return jsonify({'message': 'Document indexed successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error indexing document: {str(e)}'}), 500

@app.route('/index/<int:document_id>', methods=['DELETE'])
@require_auth
def remove_from_index(document_id):
    """Remove a document from search index"""
    try:
        index_entry = SearchIndex.query.filter_by(document_id=document_id).first()
        if index_entry:
            db.session.delete(index_entry)
            db.session.commit()
            return jsonify({'message': 'Document removed from index'}), 200
        else:
            return jsonify({'message': 'Document not found in index'}), 404
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error removing from index: {str(e)}'}), 500

@app.route('/reindex', methods=['POST'])
@require_auth
def reindex_all():
    """Reindex all documents from document service"""
    try:
        auth_header = request.headers.get('Authorization')

        # Get all documents from document service
        response = requests.get(
            f"{app.config['DOCUMENT_SERVICE_URL']}/documents?per_page=1000",
            headers={'Authorization': auth_header},
            timeout=30
        )

        if response.status_code != 200:
            return jsonify({'message': 'Failed to fetch documents'}), 500

        documents = response.json()['documents']

        # Clear existing index
        SearchIndex.query.delete()

        # Index all documents
        for doc in documents:
            content_parts = [
                doc.get('title', ''),
                doc.get('description', ''),
                doc.get('publisher', ''),
                doc.get('keywords', '')
            ]
            content = ' '.join(filter(None, content_parts))

            index_entry = SearchIndex(
                document_id=doc['id'],
                title=doc['title'],
                content=content,
                document_type=doc.get('document_type'),
                category=doc.get('category'),
                subject=doc.get('subject'),
                author=doc.get('author'),
                keywords=doc.get('keywords'),
                language=doc.get('language'),
                created_at=doc.get('created_at')
            )
            db.session.add(index_entry)

        db.session.commit()

        return jsonify({
            'message': 'Reindexing completed',
            'indexed_documents': len(documents)
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error during reindexing: {str(e)}'}), 500

@app.route('/stats', methods=['GET'])
@require_auth
def get_search_stats():
    """Get search statistics"""
    total_indexed = SearchIndex.query.count()

    # Count by document type
    type_stats = db.session.query(
        SearchIndex.document_type,
        db.func.count(SearchIndex.id)
    ).group_by(SearchIndex.document_type).all()

    # Count by category
    category_stats = db.session.query(
        SearchIndex.category,
        db.func.count(SearchIndex.id)
    ).filter(SearchIndex.category.isnot(None)).group_by(SearchIndex.category).all()

    return jsonify({
        'total_indexed': total_indexed,
        'by_type': {stat[0]: stat[1] for stat in type_stats if stat[0]},
        'by_category': {stat[0]: stat[1] for stat in category_stats if stat[0]}
    }), 200

# Initialize database
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5004)
