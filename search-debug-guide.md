# 🔍 Search Functionality Debug Guide

## ✅ Fixes Applied

### 🌙 Dark Mode Issues Fixed
- **Input fields now properly themed** with dynamic colors
- **Labels and text adapt to theme** using theme context colors
- **Buttons follow dark mode** with proper hover states
- **Error messages themed** for both light and dark modes
- **Consistent styling** across all search elements

### 🔧 Search Logic Issues Fixed
- **Added authentication check** before making API calls
- **Enhanced error handling** with specific error messages
- **Added comprehensive logging** for debugging
- **Improved parameter handling** with proper validation
- **Added debug button** (🐛) to check search state
- **Better user feedback** with loading states and error recovery

## 🧪 How to Test the Search

### 1. **Check Authentication**
- Make sure you're logged in
- Check browser console for "Auth token exists: true"

### 2. **Test Basic Search**
```javascript
// Open browser console and check:
localStorage.getItem('token') // Should return a token
```

### 3. **Use Debug Button**
- Click the 🐛 button next to Refresh
- Check console for debug information
- Verify filters, auth token, and API calls

### 4. **Test Search Scenarios**
- **Empty search**: Should load all documents
- **Text search**: Enter "book" or any title
- **Type filter**: Select "book", "periodical", etc.
- **Category filter**: Enter a category name
- **Available only**: Toggle checkbox

### 5. **Check Network Tab**
- Open DevTools → Network tab
- Perform a search
- Look for API call to: `http://localhost:5000/api/documents`
- Check request parameters and response

## 🐛 Common Issues & Solutions

### Issue 1: "Please log in to search documents"
**Solution**: User not authenticated
```javascript
// Check in console:
localStorage.getItem('token')
// If null, log in again
```

### Issue 2: No API calls in Network tab
**Solution**: Frontend not making requests
- Check console for JavaScript errors
- Verify the search form is submitting
- Click debug button to check state

### Issue 3: API returns 401/403 errors
**Solution**: Authentication/authorization issue
- Token might be expired
- User might not have permissions
- Check backend logs

### Issue 4: API returns 500 errors
**Solution**: Backend server issue
- Check if document-service is running
- Check if API gateway is running
- Verify database connection

### Issue 5: Search returns no results
**Solution**: Check search parameters
- Use debug button to see exact parameters sent
- Try searching without filters first
- Check if documents exist in database

## 🚀 Backend Services Required

Make sure these services are running:

```bash
# 1. Document Service (Port 5001)
cd microservices/document-service
python app.py

# 2. API Gateway (Port 5000)
cd microservices/api-gateway
python app.py

# 3. Auth Service (Port 5002)
cd microservices/auth-service
python app.py
```

## 📊 Debug Console Commands

Use these in browser console for debugging:

```javascript
// Check authentication
console.log('Token:', localStorage.getItem('token'));

// Check current search state
console.log('Search filters:', /* current filters */);

// Test API directly
fetch('http://localhost:5000/api/documents', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  }
})
.then(r => r.json())
.then(console.log);
```

## 🎯 Expected Behavior

### Working Search Should:
1. **Show loading state** when searching
2. **Display results** or "No documents found"
3. **Handle errors gracefully** with retry option
4. **Apply dark mode styling** correctly
5. **Log debug information** in console
6. **Make API calls** visible in Network tab

### Console Logs Should Show:
```
Filter changed: search = your-search-term
New filters state: {search: "your-search-term", ...}
Adding search term: your-search-term
Fetching documents with params: {page: 1, per_page: 6, search: "your-search-term"}
API URL will be: http://localhost:5000/api/documents
API Response received: {documents: [...], total: X, pages: Y}
Successfully loaded X documents out of Y total
```

## 🔧 Quick Fixes

If search still not working:

1. **Clear browser cache** and reload
2. **Check all services are running** on correct ports
3. **Verify database has documents** to search
4. **Check CORS settings** in API gateway
5. **Try logging out and back in** to refresh token

The search should now work properly with full dark mode support and comprehensive error handling!
